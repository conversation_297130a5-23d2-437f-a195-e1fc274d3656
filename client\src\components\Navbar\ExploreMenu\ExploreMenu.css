.explore-menu{
    display: flex;
    flex-direction: column;
    gap: 20px;
}
.explore-menu h1{
    color: #262626;
    font-weight: 500;
}
.explore-menu-text{
    max-width: 50%;
    color: #808080;
}
.explore-menu-list{
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 20px;
    text-align: center;
    margin: 20px 0px;
    overflow-x: auto;
    padding: 10px 0;
    scroll-behavior: smooth;
}
.explore-menu-list::-webkit-scrollbar{
    display: none;
}

.explore-menu-list-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;
    max-width: 120px;
    flex-shrink: 0;
}

.explore-menu-list-item .category-image-container{
    width: 80px;
    height: 80px;
    min-width: 80px;
    min-height: 80px;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: relative;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
}

.explore-menu-list-item .category-image-container:hover{
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.explore-menu-list-item .category-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 50%;
}
.explore-menu-list-item p{
    margin-top: 8px;
    color: #747474;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    line-height: 1.2;
    min-height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    word-wrap: break-word;
    hyphens: auto;
    max-width: 100px;
}
.explore-menu hr{
    margin: 10px 0px;
    height: 2px;
    background-color: #e2e2e2;
    border: none;
}
.explore-menu-list-item .category-image-container.active{
    border: 3px solid #ff6347;
    padding: 2px;
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(255, 99, 71, 0.3);
}
/* Add loading animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #ff6b35;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive design */
@media (max-width: 1050px) {
    .explore-menu-text {
        max-width: 100%;
        font-size: 14px;
    }

    .explore-menu-list {
        gap: 15px;
    }

    .explore-menu-list-item {
        min-width: 90px;
        max-width: 100px;
    }

    .explore-menu-list-item .category-image-container {
        width: 70px;
        height: 70px;
        min-width: 70px;
        min-height: 70px;
    }

    .explore-menu-list-item p {
        font-size: 12px;
        min-height: 28px;
        max-width: 90px;
    }
}

@media (max-width: 768px) {
    .explore-menu {
        margin: 0 -4px;
    }

    .explore-menu h1 {
        font-size: 22px;
        margin-bottom: 8px;
    }

    .explore-menu-text {
        font-size: 13px;
        margin-bottom: 12px;
    }

    .explore-menu-list {
        gap: 10px;
        padding: 5px 4px;
        margin: 16px 0;
    }

    .explore-menu-list-item {
        min-width: 70px;
        max-width: 80px;
    }

    .explore-menu-list-item .category-image-container {
        width: 55px;
        height: 55px;
        min-width: 55px;
        min-height: 55px;
    }

    .explore-menu-list-item p {
        font-size: 10px;
        min-height: 20px;
        max-width: 70px;
        margin-top: 4px;
    }
}

@media (max-width: 480px) {
    .explore-menu {
        margin: 0 -2px;
    }

    .explore-menu h1 {
        font-size: 20px;
        margin-bottom: 6px;
    }

    .explore-menu-text {
        font-size: 12px;
        margin-bottom: 10px;
    }

    .explore-menu-list {
        gap: 8px;
        padding: 4px 2px;
        margin: 12px 0;
    }

    .explore-menu-list-item {
        min-width: 60px;
        max-width: 70px;
    }

    .explore-menu-list-item .category-image-container {
        width: 50px;
        height: 50px;
        min-width: 50px;
        min-height: 50px;
    }

    .explore-menu-list-item p {
        font-size: 9px;
        min-height: 18px;
        max-width: 60px;
        margin-top: 3px;
    }
}

@media (max-width: 360px) {
    .explore-menu h1 {
        font-size: 18px;
        margin-bottom: 4px;
    }

    .explore-menu-text {
        font-size: 11px;
        margin-bottom: 8px;
    }

    .explore-menu-list {
        gap: 6px;
        padding: 3px 1px;
        margin: 10px 0;
    }

    .explore-menu-list-item {
        min-width: 55px;
        max-width: 65px;
    }

    .explore-menu-list-item .category-image-container {
        width: 45px;
        height: 45px;
        min-width: 45px;
        min-height: 45px;
    }

    .explore-menu-list-item p {
        font-size: 8px;
        min-height: 16px;
        max-width: 55px;
        margin-top: 2px;
    }
}